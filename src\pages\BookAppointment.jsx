import { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { useAuth } from '../contexts/AuthContext'
import { supabase } from '../lib/supabase'
import { Calendar, Clock, DollarSign, User, FileText } from 'lucide-react'
import toast from 'react-hot-toast'

const BookAppointment = () => {
  const { barberId } = useParams()
  const { user, userProfile } = useAuth()
  const navigate = useNavigate()
  const [barber, setBarber] = useState(null)
  const [services, setServices] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedService, setSelectedService] = useState(null)
  const { register, handleSubmit, formState: { errors, isSubmitting }, watch } = useForm()

  const selectedDate = watch('date')
  const selectedTime = watch('time')

  useEffect(() => {
    if (!user) {
      navigate('/login')
      return
    }
    
    if (userProfile?.role === 'barber') {
      toast.error('الحلاقين لا يمكنهم حجز مواعيد')
      navigate('/barbers')
      return
    }

    fetchBarberAndServices()
  }, [barberId, user, userProfile])

  const fetchBarberAndServices = async () => {
    try {
      // Fetch barber info
      const { data: barberData, error: barberError } = await supabase
        .from('users')
        .select('*')
        .eq('id', barberId)
        .eq('role', 'barber')
        .single()

      if (barberError) throw barberError
      setBarber(barberData)

      // Fetch services
      const { data: servicesData, error: servicesError } = await supabase
        .from('services')
        .select('*')
        .eq('is_active', true)

      if (servicesError) throw servicesError
      setServices(servicesData || [])
    } catch (error) {
      console.error('Error fetching data:', error)
      toast.error('حدث خطأ في تحميل البيانات')
      navigate('/barbers')
    } finally {
      setLoading(false)
    }
  }

  const checkAvailability = async (date, time) => {
    try {
      const { data, error } = await supabase
        .from('appointments')
        .select('id')
        .eq('barber_id', barberId)
        .eq('appointment_date', date)
        .eq('appointment_time', time)
        .in('status', ['pending', 'confirmed'])

      if (error) throw error
      return data.length === 0
    } catch (error) {
      console.error('Error checking availability:', error)
      return false
    }
  }

  const onSubmit = async (data) => {
    if (!selectedService) {
      toast.error('يرجى اختيار الخدمة')
      return
    }

    // Check availability
    const isAvailable = await checkAvailability(data.date, data.time)
    if (!isAvailable) {
      toast.error('هذا الموعد محجوز بالفعل، يرجى اختيار موعد آخر')
      return
    }

    try {
      const { data: appointmentData, error } = await supabase
        .from('appointments')
        .insert([
          {
            client_id: user.id,
            barber_id: barberId,
            appointment_date: data.date,
            appointment_time: data.time,
            service_type: selectedService.name,
            service_price: selectedService.price,
            notes: data.notes || null,
            status: 'pending'
          }
        ])
        .select()
        .single()

      if (error) throw error

      toast.success('تم إرسال طلب الحجز بنجاح!')
      navigate('/appointments')
    } catch (error) {
      console.error('Error creating appointment:', error)
      toast.error('حدث خطأ في إنشاء الموعد')
    }
  }

  // Generate time slots
  const generateTimeSlots = () => {
    const slots = []
    for (let hour = 9; hour <= 21; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
        slots.push(time)
      }
    }
    return slots
  }

  // Get minimum date (today)
  const getMinDate = () => {
    const today = new Date()
    return today.toISOString().split('T')[0]
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    )
  }

  if (!barber) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">الحلاق غير موجود</h2>
          <button 
            onClick={() => navigate('/barbers')}
            className="bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors duration-200"
          >
            العودة للحلاقين
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-black mb-2">حجز موعد</h1>
          <p className="text-gray-600">احجز موعدك مع {barber.full_name}</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Barber Info */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-lg p-6 sticky top-6">
              <div className="text-center mb-6">
                <div className="w-24 h-24 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
                  {barber.profile_image_url ? (
                    <img 
                      src={barber.profile_image_url} 
                      alt={barber.full_name}
                      className="w-full h-full object-cover rounded-full"
                    />
                  ) : (
                    <User className="w-12 h-12 text-gray-400" />
                  )}
                </div>
                <h3 className="text-xl font-bold text-black">{barber.full_name}</h3>
                {barber.experience_years && (
                  <p className="text-gray-600">{barber.experience_years} سنوات خبرة</p>
                )}
              </div>

              {barber.bio && (
                <div className="mb-4">
                  <h4 className="font-semibold text-black mb-2">نبذة</h4>
                  <p className="text-gray-600 text-sm">{barber.bio}</p>
                </div>
              )}

              {barber.specialties && barber.specialties.length > 0 && (
                <div>
                  <h4 className="font-semibold text-black mb-2">التخصصات</h4>
                  <div className="flex flex-wrap gap-2">
                    {barber.specialties.map((specialty, index) => (
                      <span 
                        key={index}
                        className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs"
                      >
                        {specialty}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Booking Form */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-lg p-6">
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Service Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">اختر الخدمة</label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {services.map((service) => (
                      <div
                        key={service.id}
                        onClick={() => setSelectedService(service)}
                        className={`p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                          selectedService?.id === service.id
                            ? 'border-black bg-black text-white'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        <h4 className="font-semibold mb-1">{service.name}</h4>
                        <div className="flex justify-between items-center text-sm">
                          <span>{service.price} جنيه</span>
                          <span>{service.duration_minutes} دقيقة</span>
                        </div>
                      </div>
                    ))}
                  </div>
                  {!selectedService && (
                    <p className="mt-1 text-sm text-red-600">يرجى اختيار الخدمة</p>
                  )}
                </div>

                {/* Date Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">التاريخ</label>
                  <div className="relative">
                    <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <input
                      type="date"
                      {...register('date', { required: 'التاريخ مطلوب' })}
                      min={getMinDate()}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                    />
                  </div>
                  {errors.date && (
                    <p className="mt-1 text-sm text-red-600">{errors.date.message}</p>
                  )}
                </div>

                {/* Time Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الوقت</label>
                  <div className="relative">
                    <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <select
                      {...register('time', { required: 'الوقت مطلوب' })}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                    >
                      <option value="">اختر الوقت</option>
                      {generateTimeSlots().map((time) => (
                        <option key={time} value={time}>
                          {time}
                        </option>
                      ))}
                    </select>
                  </div>
                  {errors.time && (
                    <p className="mt-1 text-sm text-red-600">{errors.time.message}</p>
                  )}
                </div>

                {/* Notes */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">ملاحظات (اختياري)</label>
                  <div className="relative">
                    <FileText className="absolute left-3 top-3 text-gray-400 w-5 h-5" />
                    <textarea
                      {...register('notes')}
                      rows={3}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                      placeholder="أي ملاحظات خاصة أو طلبات..."
                    />
                  </div>
                </div>

                {/* Summary */}
                {selectedService && selectedDate && selectedTime && (
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-black mb-2">ملخص الحجز</h4>
                    <div className="space-y-1 text-sm text-gray-600">
                      <p><strong>الحلاق:</strong> {barber.full_name}</p>
                      <p><strong>الخدمة:</strong> {selectedService.name}</p>
                      <p><strong>التاريخ:</strong> {selectedDate}</p>
                      <p><strong>الوقت:</strong> {selectedTime}</p>
                      <p><strong>المدة:</strong> {selectedService.duration_minutes} دقيقة</p>
                      <p className="text-lg font-semibold text-black">
                        <strong>السعر:</strong> {selectedService.price} جنيه
                      </p>
                    </div>
                  </div>
                )}

                <button
                  type="submit"
                  disabled={isSubmitting || !selectedService}
                  className="w-full bg-black text-white py-3 rounded-lg hover:bg-gray-800 transition-colors duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                >
                  <Calendar className="w-5 h-5" />
                  {isSubmitting ? 'جاري إرسال الطلب...' : 'تأكيد الحجز'}
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BookAppointment
