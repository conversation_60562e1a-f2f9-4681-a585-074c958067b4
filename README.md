# Picasso - موقع حلاقة احترافي

موقع حلاقة عصري وفخم باسم "Picasso" يوفر نظام حجز مواعيد متكامل للعملاء والحلاقين.

## 🎯 المميزات

### للعملاء:
- ✅ تسجيل حساب جديد وتسجيل دخول آمن
- ✅ تصفح الحلاقين المتاحين ومعلوماتهم
- ✅ حجز مواعيد مع اختيار التاريخ والوقت
- ✅ اختيار الخدمة المطلوبة من قائمة شاملة
- ✅ دفع عبر Vodafone Cash مع رفع إيصال الدفع
- ✅ متابعة حالة المواعيد (قيد المراجعة - مؤكد - مرفوض)
- ✅ لوحة تحكم شخصية لإدارة المواعيد

### للحلاقين:
- ✅ تسجيل حساب كحلاق مع معلومات مهنية
- ✅ إدارة المواعيد والطلبات الواردة
- ✅ قبول أو رفض طلبات الحجز
- ✅ مراجعة إيصالات الدفع
- ✅ عرض الملف الشخصي والتخصصات

## 🛠️ التقنيات المستخدمة

### Frontend:
- **React 19** - مكتبة واجهة المستخدم
- **Vite** - أداة البناء السريعة
- **Tailwind CSS** - إطار عمل CSS للتصميم
- **React Router** - التنقل بين الصفحات
- **React Hook Form** - إدارة النماذج
- **Lucide React** - الأيقونات
- **React Hot Toast** - الإشعارات

### Backend & Database:
- **Supabase** - قاعدة البيانات والمصادقة
- **PostgreSQL** - قاعدة البيانات
- **Row Level Security (RLS)** - الأمان على مستوى الصفوف
- **Supabase Storage** - تخزين الملفات

## 🚀 التشغيل

### المتطلبات:
- Node.js 18+
- npm أو yarn

### خطوات التشغيل:

1. **تثبيت المكتبات:**
```bash
npm install
```

2. **تشغيل المشروع:**
```bash
npm run dev
```

3. **فتح المتصفح:**
```
http://localhost:5173
```

## 🔐 حسابات تجريبية

للاختبار، يمكنك استخدام الحسابات التالية:

**عميل:**
- البريد: `<EMAIL>`
- كلمة المرور: `123456`

**حلاق:**
- البريد: `<EMAIL>`
- كلمة المرور: `123456`

## 📊 قاعدة البيانات

### الجداول الرئيسية:

1. **users** - معلومات المستخدمين (عملاء وحلاقين)
2. **appointments** - المواعيد والحجوزات
3. **payments** - معلومات الدفع والإيصالات
4. **services** - الخدمات المتاحة
5. **barber_services** - ربط الحلاقين بالخدمات

### الأمان:
- تم تفعيل Row Level Security على جميع الجداول
- سياسات أمان مخصصة لكل نوع مستخدم
- حماية البيانات الشخصية والمالية

## 🎨 التصميم

- **الألوان:** أسود وأبيض (تصميم مونوكروم فخم)
- **الخط:** Cairo (خط عربي أنيق)
- **التصميم:** Responsive للموبايل والكمبيوتر
- **التأثيرات:** Hover effects وانتقالات سلسة
- **الأيقونات:** Lucide React icons

## 📱 التوافق

- ✅ متوافق مع جميع المتصفحات الحديثة
- ✅ تصميم متجاوب للموبايل والتابلت
- ✅ سرعة تحميل عالية
- ✅ تجربة مستخدم سلسة

## 🔧 البناء للإنتاج

```bash
npm run build
```

## 📄 الترخيص

هذا المشروع مطور خصيصاً لصالون Picasso.

---

**تم التطوير بواسطة:** Augment Agent
**التاريخ:** 2024
