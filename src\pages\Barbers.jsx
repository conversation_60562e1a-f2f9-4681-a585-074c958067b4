import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { supabase } from '../lib/supabase'
import { Star, Clock, MapPin, Calendar, User } from 'lucide-react'

const Barbers = () => {
  const [barbers, setBarbers] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    fetchBarbers()
  }, [])

  const fetchBarbers = async () => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('role', 'barber')
        .eq('is_available', true)

      if (error) throw error
      setBarbers(data || [])
    } catch (error) {
      console.error('Error fetching barbers:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredBarbers = barbers.filter(barber =>
    barber.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (barber.specialties && barber.specialties.some(specialty => 
      specialty.toLowerCase().includes(searchTerm.toLowerCase())
    ))
  )

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل الحلاقين...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-black mb-4">حلاقينا المحترفين</h1>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            اختر من بين أمهر الحلاقين واحجز موعدك مع الأفضل
          </p>
        </div>

        {/* Search */}
        <div className="max-w-md mx-auto mb-12">
          <div className="relative">
            <input
              type="text"
              placeholder="ابحث عن حلاق أو تخصص..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-3 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
            />
            <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          </div>
        </div>

        {/* Barbers Grid */}
        {filteredBarbers.length === 0 ? (
          <div className="text-center py-12">
            <User className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">لا توجد نتائج</h3>
            <p className="text-gray-500">جرب البحث بكلمات مختلفة</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredBarbers.map((barber) => (
              <div 
                key={barber.id}
                className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
              >
                {/* Profile Image */}
                <div className="h-48 bg-gradient-to-br from-gray-200 to-gray-300 relative overflow-hidden">
                  {barber.profile_image_url ? (
                    <img 
                      src={barber.profile_image_url} 
                      alt={barber.full_name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <User className="w-16 h-16 text-gray-400" />
                    </div>
                  )}
                  <div className="absolute top-4 right-4 bg-black text-white px-2 py-1 rounded-full text-xs">
                    متاح
                  </div>
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-black mb-2">{barber.full_name}</h3>
                  
                  {/* Experience */}
                  {barber.experience_years && (
                    <div className="flex items-center text-gray-600 mb-2">
                      <Clock className="w-4 h-4 ml-1" />
                      <span className="text-sm">{barber.experience_years} سنوات خبرة</span>
                    </div>
                  )}

                  {/* Bio */}
                  {barber.bio && (
                    <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                      {barber.bio}
                    </p>
                  )}

                  {/* Specialties */}
                  {barber.specialties && barber.specialties.length > 0 && (
                    <div className="mb-4">
                      <div className="flex flex-wrap gap-2">
                        {barber.specialties.slice(0, 3).map((specialty, index) => (
                          <span 
                            key={index}
                            className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs"
                          >
                            {specialty}
                          </span>
                        ))}
                        {barber.specialties.length > 3 && (
                          <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs">
                            +{barber.specialties.length - 3}
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Rating (placeholder) */}
                  <div className="flex items-center mb-4">
                    <div className="flex text-yellow-400">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 fill-current" />
                      ))}
                    </div>
                    <span className="text-gray-600 text-sm mr-2">(4.8)</span>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    <Link 
                      to={`/barber/${barber.id}`}
                      className="flex-1 bg-gray-100 text-black text-center py-2 rounded-lg hover:bg-gray-200 transition-colors duration-200 text-sm font-medium"
                    >
                      عرض الملف
                    </Link>
                    <Link 
                      to={`/book/${barber.id}`}
                      className="flex-1 bg-black text-white text-center py-2 rounded-lg hover:bg-gray-800 transition-colors duration-200 text-sm font-medium flex items-center justify-center gap-1"
                    >
                      <Calendar className="w-4 h-4" />
                      احجز الآن
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* CTA Section */}
        <div className="text-center mt-16 bg-white rounded-xl p-8 shadow-lg">
          <h2 className="text-2xl font-bold text-black mb-4">هل أنت حلاق محترف؟</h2>
          <p className="text-gray-600 mb-6">انضم إلى فريقنا وابدأ في استقبال العملاء</p>
          <Link 
            to="/register" 
            className="bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors duration-200 inline-flex items-center gap-2"
          >
            <User className="w-5 h-5" />
            انضم كحلاق
          </Link>
        </div>
      </div>
    </div>
  )
}

export default Barbers
