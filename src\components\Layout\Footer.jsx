import { Link } from 'react-router-dom'
import { Phone, Mail, MapPin, Clock } from 'lucide-react'

const Footer = () => {
  return (
    <footer className="bg-black text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <div className="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              Picasso
            </div>
            <p className="text-gray-400 text-sm leading-relaxed">
              صالون حلاقة عصري يقدم أفضل الخدمات بأحدث التقنيات والأساليب العالمية
            </p>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">روابط سريعة</h3>
            <div className="space-y-2">
              <Link 
                to="/" 
                className="block text-gray-400 hover:text-white transition-colors duration-200"
              >
                الرئيسية
              </Link>
              <Link 
                to="/barbers" 
                className="block text-gray-400 hover:text-white transition-colors duration-200"
              >
                الحلاقين
              </Link>
              <Link 
                to="/services" 
                className="block text-gray-400 hover:text-white transition-colors duration-200"
              >
                الخدمات
              </Link>
              <Link 
                to="/about" 
                className="block text-gray-400 hover:text-white transition-colors duration-200"
              >
                من نحن
              </Link>
            </div>
          </div>

          {/* Services */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">خدماتنا</h3>
            <div className="space-y-2 text-gray-400 text-sm">
              <p>قص شعر كلاسيكي وعصري</p>
              <p>حلاقة ذقن احترافية</p>
              <p>تصفيف وتنظيف الشعر</p>
              <p>صبغة وماسك للشعر</p>
            </div>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">تواصل معنا</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3 text-gray-400">
                <Phone size={16} />
                <span className="text-sm">+20 ************</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-400">
                <Mail size={16} />
                <span className="text-sm"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3 text-gray-400">
                <MapPin size={16} />
                <span className="text-sm">القاهرة، مصر</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-400">
                <Clock size={16} />
                <span className="text-sm">يومياً 9 ص - 11 م</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-gray-400 text-sm">
              © 2024 Picasso Salon. جميع الحقوق محفوظة.
            </p>
            <div className="flex space-x-6 text-sm">
              <Link 
                to="/privacy" 
                className="text-gray-400 hover:text-white transition-colors duration-200"
              >
                سياسة الخصوصية
              </Link>
              <Link 
                to="/terms" 
                className="text-gray-400 hover:text-white transition-colors duration-200"
              >
                الشروط والأحكام
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
