import { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import { Menu, X, User, LogOut, Calendar, Settings } from 'lucide-react'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const { user, userProfile, signOut } = useAuth()
  const navigate = useNavigate()

  const handleSignOut = async () => {
    await signOut()
    navigate('/')
    setIsMenuOpen(false)
  }

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  return (
    <header className="bg-black text-white shadow-lg sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <div className="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              Picasso
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link 
              to="/" 
              className="hover:text-gray-300 transition-colors duration-200"
            >
              الرئيسية
            </Link>
            <Link 
              to="/barbers" 
              className="hover:text-gray-300 transition-colors duration-200"
            >
              الحلاقين
            </Link>
            <Link 
              to="/services" 
              className="hover:text-gray-300 transition-colors duration-200"
            >
              الخدمات
            </Link>
            {user && (
              <Link 
                to="/appointments" 
                className="hover:text-gray-300 transition-colors duration-200"
              >
                مواعيدي
              </Link>
            )}
          </nav>

          {/* User Menu */}
          <div className="hidden md:flex items-center space-x-4">
            {user ? (
              <div className="relative group">
                <button className="flex items-center space-x-2 hover:text-gray-300 transition-colors duration-200">
                  <User size={20} />
                  <span>{userProfile?.full_name || 'المستخدم'}</span>
                </button>
                <div className="absolute left-0 mt-2 w-48 bg-white text-black rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                  <Link 
                    to="/profile" 
                    className="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors duration-200"
                  >
                    <Settings size={16} className="ml-2" />
                    الملف الشخصي
                  </Link>
                  {userProfile?.role === 'barber' && (
                    <Link 
                      to="/barber-dashboard" 
                      className="flex items-center px-4 py-2 hover:bg-gray-100 transition-colors duration-200"
                    >
                      <Calendar size={16} className="ml-2" />
                      لوحة التحكم
                    </Link>
                  )}
                  <button 
                    onClick={handleSignOut}
                    className="flex items-center w-full px-4 py-2 hover:bg-gray-100 transition-colors duration-200 text-red-600"
                  >
                    <LogOut size={16} className="ml-2" />
                    تسجيل الخروج
                  </button>
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <Link 
                  to="/login" 
                  className="hover:text-gray-300 transition-colors duration-200"
                >
                  تسجيل الدخول
                </Link>
                <Link 
                  to="/register" 
                  className="bg-white text-black px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors duration-200"
                >
                  إنشاء حساب
                </Link>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button 
            onClick={toggleMenu}
            className="md:hidden p-2 hover:bg-gray-800 rounded-lg transition-colors duration-200"
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-800">
            <nav className="flex flex-col space-y-4">
              <Link 
                to="/" 
                onClick={() => setIsMenuOpen(false)}
                className="hover:text-gray-300 transition-colors duration-200"
              >
                الرئيسية
              </Link>
              <Link 
                to="/barbers" 
                onClick={() => setIsMenuOpen(false)}
                className="hover:text-gray-300 transition-colors duration-200"
              >
                الحلاقين
              </Link>
              <Link 
                to="/services" 
                onClick={() => setIsMenuOpen(false)}
                className="hover:text-gray-300 transition-colors duration-200"
              >
                الخدمات
              </Link>
              {user ? (
                <>
                  <Link 
                    to="/appointments" 
                    onClick={() => setIsMenuOpen(false)}
                    className="hover:text-gray-300 transition-colors duration-200"
                  >
                    مواعيدي
                  </Link>
                  <Link 
                    to="/profile" 
                    onClick={() => setIsMenuOpen(false)}
                    className="hover:text-gray-300 transition-colors duration-200"
                  >
                    الملف الشخصي
                  </Link>
                  {userProfile?.role === 'barber' && (
                    <Link 
                      to="/barber-dashboard" 
                      onClick={() => setIsMenuOpen(false)}
                      className="hover:text-gray-300 transition-colors duration-200"
                    >
                      لوحة التحكم
                    </Link>
                  )}
                  <button 
                    onClick={handleSignOut}
                    className="text-left hover:text-gray-300 transition-colors duration-200 text-red-400"
                  >
                    تسجيل الخروج
                  </button>
                </>
              ) : (
                <>
                  <Link 
                    to="/login" 
                    onClick={() => setIsMenuOpen(false)}
                    className="hover:text-gray-300 transition-colors duration-200"
                  >
                    تسجيل الدخول
                  </Link>
                  <Link 
                    to="/register" 
                    onClick={() => setIsMenuOpen(false)}
                    className="bg-white text-black px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors duration-200 text-center"
                  >
                    إنشاء حساب
                  </Link>
                </>
              )}
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}

export default Header
