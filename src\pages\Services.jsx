import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { supabase } from '../lib/supabase'
import { Clock, DollarSign, Scissors, Calendar } from 'lucide-react'

const Services = () => {
  const [services, setServices] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchServices()
  }, [])

  const fetchServices = async () => {
    try {
      const { data, error } = await supabase
        .from('services')
        .select('*')
        .eq('is_active', true)
        .order('price', { ascending: true })

      if (error) throw error
      setServices(data || [])
    } catch (error) {
      console.error('Error fetching services:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل الخدمات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-black mb-4">خدماتنا المميزة</h1>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            نقدم مجموعة شاملة من خدمات الحلاقة والتجميل بأعلى معايير الجودة
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {services.map((service) => (
            <div 
              key={service.id}
              className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
            >
              {/* Service Icon */}
              <div className="h-32 bg-gradient-to-br from-black to-gray-800 flex items-center justify-center">
                <Scissors className="w-12 h-12 text-white" />
              </div>

              {/* Content */}
              <div className="p-6">
                <h3 className="text-xl font-bold text-black mb-3">{service.name}</h3>
                
                {service.description && (
                  <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                    {service.description}
                  </p>
                )}

                {/* Service Details */}
                <div className="space-y-3 mb-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-gray-600">
                      <DollarSign className="w-4 h-4 ml-1" />
                      <span className="text-sm">السعر</span>
                    </div>
                    <span className="font-bold text-black text-lg">{service.price} جنيه</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-gray-600">
                      <Clock className="w-4 h-4 ml-1" />
                      <span className="text-sm">المدة</span>
                    </div>
                    <span className="text-gray-800">{service.duration_minutes} دقيقة</span>
                  </div>
                </div>

                {/* Action Button */}
                <Link 
                  to="/barbers"
                  className="w-full bg-black text-white text-center py-3 rounded-lg hover:bg-gray-800 transition-colors duration-200 font-medium flex items-center justify-center gap-2"
                >
                  <Calendar className="w-4 h-4" />
                  احجز الآن
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* Features Section */}
        <div className="bg-white rounded-xl shadow-lg p-8 mb-16">
          <h2 className="text-3xl font-bold text-black text-center mb-8">لماذا تختار خدماتنا؟</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-black rounded-full flex items-center justify-center mx-auto mb-4">
                <Scissors className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-black mb-2">أدوات احترافية</h3>
              <p className="text-gray-600 text-sm">
                نستخدم أحدث الأدوات والمعدات الاحترافية لضمان أفضل النتائج
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-black rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-black mb-2">خدمة سريعة</h3>
              <p className="text-gray-600 text-sm">
                نحترم وقتك ونقدم خدمات عالية الجودة في أقل وقت ممكن
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-black rounded-full flex items-center justify-center mx-auto mb-4">
                <DollarSign className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-black mb-2">أسعار مناسبة</h3>
              <p className="text-gray-600 text-sm">
                أسعار تنافسية مع جودة عالية تناسب جميع الفئات
              </p>
            </div>
          </div>
        </div>

        {/* Packages Section */}
        <div className="bg-gradient-to-br from-black to-gray-800 text-white rounded-xl p-8">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold mb-4">باقات مميزة</h2>
            <p className="text-gray-300">
              احصل على خصومات رائعة مع باقاتنا المتنوعة
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white bg-opacity-10 rounded-lg p-6 backdrop-blur-sm">
              <h3 className="text-xl font-bold mb-3">باقة الأساسية</h3>
              <p className="text-gray-300 text-sm mb-4">قص شعر + حلاقة ذقن</p>
              <div className="flex items-center justify-between mb-4">
                <span className="text-2xl font-bold">70 جنيه</span>
                <span className="text-sm text-gray-300 line-through">80 جنيه</span>
              </div>
              <Link 
                to="/barbers"
                className="w-full bg-white text-black text-center py-2 rounded-lg hover:bg-gray-200 transition-colors duration-200 font-medium"
              >
                احجز الآن
              </Link>
            </div>

            <div className="bg-white bg-opacity-10 rounded-lg p-6 backdrop-blur-sm">
              <h3 className="text-xl font-bold mb-3">باقة المميزة</h3>
              <p className="text-gray-300 text-sm mb-4">قص شعر عصري + حلاقة + تصفيف</p>
              <div className="flex items-center justify-between mb-4">
                <span className="text-2xl font-bold">100 جنيه</span>
                <span className="text-sm text-gray-300 line-through">130 جنيه</span>
              </div>
              <Link 
                to="/barbers"
                className="w-full bg-white text-black text-center py-2 rounded-lg hover:bg-gray-200 transition-colors duration-200 font-medium"
              >
                احجز الآن
              </Link>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <h2 className="text-3xl font-bold text-black mb-4">جاهز لتجربة مميزة؟</h2>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            اختر الخدمة المناسبة لك واحجز موعدك مع أفضل الحلاقين
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              to="/barbers" 
              className="bg-black text-white px-8 py-3 rounded-lg hover:bg-gray-800 transition-colors duration-200 inline-flex items-center justify-center gap-2"
            >
              <Calendar className="w-5 h-5" />
              احجز موعدك
            </Link>
            <Link 
              to="/register" 
              className="border-2 border-black text-black px-8 py-3 rounded-lg hover:bg-black hover:text-white transition-colors duration-200 inline-flex items-center justify-center gap-2"
            >
              إنشاء حساب
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Services
