import { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { supabase } from '../lib/supabase'
import { Calendar, Clock, User, DollarSign, FileText, X, Upload } from 'lucide-react'
import toast from 'react-hot-toast'

const Appointments = () => {
  const { user, userProfile } = useAuth()
  const [appointments, setAppointments] = useState([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState('all')
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [selectedAppointment, setSelectedAppointment] = useState(null)
  const [receiptFile, setReceiptFile] = useState(null)
  const [uploading, setUploading] = useState(false)

  useEffect(() => {
    if (user) {
      fetchAppointments()
    }
  }, [user])

  const fetchAppointments = async () => {
    try {
      const { data, error } = await supabase
        .from('appointments')
        .select(`
          *,
          barber:barber_id(full_name, phone),
          client:client_id(full_name, phone),
          payments(*)
        `)
        .eq(userProfile?.role === 'barber' ? 'barber_id' : 'client_id', user.id)
        .order('appointment_date', { ascending: false })
        .order('appointment_time', { ascending: false })

      if (error) throw error
      setAppointments(data || [])
    } catch (error) {
      console.error('Error fetching appointments:', error)
      toast.error('حدث خطأ في تحميل المواعيد')
    } finally {
      setLoading(false)
    }
  }

  const updateAppointmentStatus = async (appointmentId, status) => {
    try {
      const { error } = await supabase
        .from('appointments')
        .update({ status, updated_at: new Date().toISOString() })
        .eq('id', appointmentId)

      if (error) throw error
      
      await fetchAppointments()
      toast.success(`تم ${status === 'confirmed' ? 'قبول' : 'رفض'} الموعد`)
    } catch (error) {
      console.error('Error updating appointment:', error)
      toast.error('حدث خطأ في تحديث الموعد')
    }
  }

  const handlePayment = (appointment) => {
    setSelectedAppointment(appointment)
    setShowPaymentModal(true)
  }

  const uploadReceipt = async () => {
    if (!receiptFile || !selectedAppointment) return

    setUploading(true)
    try {
      // Upload file to Supabase Storage
      const fileExt = receiptFile.name.split('.').pop()
      const fileName = `${selectedAppointment.id}_${Date.now()}.${fileExt}`
      
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('receipts')
        .upload(fileName, receiptFile)

      if (uploadError) throw uploadError

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('receipts')
        .getPublicUrl(fileName)

      // Create payment record
      const { error: paymentError } = await supabase
        .from('payments')
        .insert([
          {
            appointment_id: selectedAppointment.id,
            amount: selectedAppointment.service_price,
            payment_method: 'vodafone_cash',
            receipt_image_url: publicUrl,
            payment_status: 'pending'
          }
        ])

      if (paymentError) throw paymentError

      toast.success('تم رفع إيصال الدفع بنجاح!')
      setShowPaymentModal(false)
      setReceiptFile(null)
      await fetchAppointments()
    } catch (error) {
      console.error('Error uploading receipt:', error)
      toast.error('حدث خطأ في رفع الإيصال')
    } finally {
      setUploading(false)
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'confirmed': return 'bg-green-100 text-green-800'
      case 'rejected': return 'bg-red-100 text-red-800'
      case 'completed': return 'bg-blue-100 text-blue-800'
      case 'cancelled': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'pending': return 'قيد المراجعة'
      case 'confirmed': return 'مؤكد'
      case 'rejected': return 'مرفوض'
      case 'completed': return 'مكتمل'
      case 'cancelled': return 'ملغي'
      default: return status
    }
  }

  const filteredAppointments = appointments.filter(appointment => {
    if (filter === 'all') return true
    return appointment.status === filter
  })

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل المواعيد...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-black mb-2">
            {userProfile?.role === 'barber' ? 'مواعيد العملاء' : 'مواعيدي'}
          </h1>
          <p className="text-gray-600">
            {userProfile?.role === 'barber' 
              ? 'إدارة مواعيد العملاء وطلبات الحجز' 
              : 'تتبع مواعيدك وحالة الحجوزات'
            }
          </p>
        </div>

        {/* Filter Tabs */}
        <div className="flex flex-wrap justify-center gap-2 mb-8">
          {[
            { key: 'all', label: 'الكل' },
            { key: 'pending', label: 'قيد المراجعة' },
            { key: 'confirmed', label: 'مؤكد' },
            { key: 'completed', label: 'مكتمل' },
            { key: 'rejected', label: 'مرفوض' }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setFilter(tab.key)}
              className={`px-4 py-2 rounded-lg transition-colors duration-200 ${
                filter === tab.key
                  ? 'bg-black text-white'
                  : 'bg-white text-gray-600 hover:bg-gray-100'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Appointments List */}
        {filteredAppointments.length === 0 ? (
          <div className="text-center py-12">
            <Calendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">لا توجد مواعيد</h3>
            <p className="text-gray-500">
              {filter === 'all' ? 'لم تقم بحجز أي مواعيد بعد' : `لا توجد مواعيد ${getStatusText(filter)}`}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredAppointments.map((appointment) => (
              <div 
                key={appointment.id}
                className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300"
              >
                {/* Status Badge */}
                <div className="flex justify-between items-start mb-4">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`}>
                    {getStatusText(appointment.status)}
                  </span>
                  <div className="text-right text-sm text-gray-500">
                    #{appointment.id.slice(-8)}
                  </div>
                </div>

                {/* Appointment Details */}
                <div className="space-y-3">
                  <div className="flex items-center text-gray-600">
                    <User className="w-4 h-4 ml-2" />
                    <span className="text-sm">
                      {userProfile?.role === 'barber' 
                        ? appointment.client?.full_name 
                        : appointment.barber?.full_name
                      }
                    </span>
                  </div>

                  <div className="flex items-center text-gray-600">
                    <Calendar className="w-4 h-4 ml-2" />
                    <span className="text-sm">{appointment.appointment_date}</span>
                  </div>

                  <div className="flex items-center text-gray-600">
                    <Clock className="w-4 h-4 ml-2" />
                    <span className="text-sm">{appointment.appointment_time}</span>
                  </div>

                  <div className="flex items-center text-gray-600">
                    <DollarSign className="w-4 h-4 ml-2" />
                    <span className="text-sm">{appointment.service_type} - {appointment.service_price} جنيه</span>
                  </div>

                  {appointment.notes && (
                    <div className="flex items-start text-gray-600">
                      <FileText className="w-4 h-4 ml-2 mt-0.5" />
                      <span className="text-sm">{appointment.notes}</span>
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="mt-6 space-y-2">
                  {userProfile?.role === 'barber' && appointment.status === 'pending' && (
                    <div className="flex gap-2">
                      <button
                        onClick={() => updateAppointmentStatus(appointment.id, 'confirmed')}
                        className="flex-1 bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition-colors duration-200 text-sm"
                      >
                        قبول
                      </button>
                      <button
                        onClick={() => updateAppointmentStatus(appointment.id, 'rejected')}
                        className="flex-1 bg-red-600 text-white py-2 rounded-lg hover:bg-red-700 transition-colors duration-200 text-sm"
                      >
                        رفض
                      </button>
                    </div>
                  )}

                  {userProfile?.role === 'client' && 
                   appointment.status === 'confirmed' && 
                   !appointment.payments?.length && (
                    <button
                      onClick={() => handlePayment(appointment)}
                      className="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 text-sm flex items-center justify-center gap-2"
                    >
                      <Upload className="w-4 h-4" />
                      رفع إيصال الدفع
                    </button>
                  )}

                  {appointment.payments?.length > 0 && (
                    <div className="text-center text-sm text-gray-600">
                      حالة الدفع: {appointment.payments[0].payment_status === 'pending' ? 'قيد المراجعة' : 'مؤكد'}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Payment Modal */}
        {showPaymentModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-xl p-6 max-w-md w-full">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-bold text-black">رفع إيصال الدفع</h3>
                <button
                  onClick={() => setShowPaymentModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              <div className="mb-4">
                <p className="text-sm text-gray-600 mb-2">
                  المبلغ المطلوب: {selectedAppointment?.service_price} جنيه
                </p>
                <p className="text-sm text-gray-600 mb-4">
                  ادفع عن طريق Vodafone Cash ثم ارفع صورة الإيصال
                </p>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  صورة الإيصال
                </label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => setReceiptFile(e.target.files[0])}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-black focus:border-transparent"
                />
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => setShowPaymentModal(false)}
                  className="flex-1 bg-gray-200 text-gray-800 py-2 rounded-lg hover:bg-gray-300 transition-colors duration-200"
                >
                  إلغاء
                </button>
                <button
                  onClick={uploadReceipt}
                  disabled={!receiptFile || uploading}
                  className="flex-1 bg-black text-white py-2 rounded-lg hover:bg-gray-800 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {uploading ? 'جاري الرفع...' : 'رفع الإيصال'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default Appointments
