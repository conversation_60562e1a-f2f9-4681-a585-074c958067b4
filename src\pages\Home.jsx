import { Link } from 'react-router-dom'
import { Scissors, Clock, Star, Users, Calendar, Shield } from 'lucide-react'

const Home = () => {
  const features = [
    {
      icon: <Scissors className="w-8 h-8" />,
      title: "خدمات احترافية",
      description: "نقدم أفضل خدمات الحلاقة بأحدث التقنيات والأساليب العالمية"
    },
    {
      icon: <Clock className="w-8 h-8" />,
      title: "حجز سهل ومرن",
      description: "احجز موعدك بسهولة واختر الوقت المناسب لك"
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "حلاقين خبراء",
      description: "فريق من أمهر الحلاقين ذوي الخبرة والمهارة العالية"
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: "أمان وثقة",
      description: "نضمن لك تجربة آمنة ومريحة مع أعلى معايير النظافة"
    }
  ]

  const services = [
    {
      name: "قص شعر كلاسيكي",
      price: "50 جنيه",
      duration: "30 دقيقة"
    },
    {
      name: "قص شعر عصري",
      price: "80 جنيه",
      duration: "45 دقيقة"
    },
    {
      name: "حلاقة ذقن",
      price: "30 جنيه",
      duration: "20 دقيقة"
    },
    {
      name: "قص + حلاقة",
      price: "70 جنيه",
      duration: "50 دقيقة"
    }
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-black via-gray-900 to-black text-white py-20 overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-50"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-white via-gray-200 to-white bg-clip-text text-transparent">
              Picasso
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-gray-300 leading-relaxed">
              صالون حلاقة عصري يجمع بين الفن والأناقة
            </p>
            <p className="text-lg mb-10 text-gray-400 max-w-2xl mx-auto">
              اكتشف تجربة حلاقة فريدة مع أمهر الحلاقين وأحدث التقنيات في أجواء فخمة وعصرية
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                to="/register" 
                className="bg-white text-black px-8 py-4 rounded-lg font-semibold hover:bg-gray-200 transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                احجز موعدك الآن
              </Link>
              <Link 
                to="/barbers" 
                className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-black transition-all duration-300 transform hover:scale-105"
              >
                تصفح الحلاقين
              </Link>
            </div>
          </div>
        </div>
        
        {/* Decorative Elements */}
        <div className="absolute top-10 left-10 w-20 h-20 border border-white opacity-20 rotate-45"></div>
        <div className="absolute bottom-10 right-10 w-16 h-16 border border-white opacity-20 rotate-12"></div>
        <div className="absolute top-1/2 left-1/4 w-2 h-2 bg-white opacity-30 rounded-full"></div>
        <div className="absolute top-1/3 right-1/3 w-3 h-3 bg-white opacity-20 rounded-full"></div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-black mb-4">لماذا تختار Picasso؟</h2>
            <p className="text-gray-600 text-lg max-w-2xl mx-auto">
              نحن نقدم تجربة حلاقة استثنائية تجمع بين الجودة والراحة والأناقة
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div 
                key={index}
                className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 text-center"
              >
                <div className="text-black mb-4 flex justify-center">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-black mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Preview */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-black mb-4">خدماتنا المميزة</h2>
            <p className="text-gray-600 text-lg max-w-2xl mx-auto">
              مجموعة متنوعة من الخدمات الاحترافية بأسعار مناسبة
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {services.map((service, index) => (
              <div 
                key={index}
                className="bg-gray-50 p-6 rounded-lg hover:bg-gray-100 transition-colors duration-300"
              >
                <h3 className="text-lg font-semibold text-black mb-2">
                  {service.name}
                </h3>
                <div className="flex justify-between items-center text-gray-600">
                  <span className="font-bold text-black">{service.price}</span>
                  <span className="text-sm">{service.duration}</span>
                </div>
              </div>
            ))}
          </div>
          
          <div className="text-center">
            <Link 
              to="/services" 
              className="bg-black text-white px-8 py-3 rounded-lg hover:bg-gray-800 transition-colors duration-300 inline-flex items-center gap-2"
            >
              <Calendar size={20} />
              عرض جميع الخدمات
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-black text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-6">جاهز لتجربة مميزة؟</h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            انضم إلى آلاف العملاء الراضين واحجز موعدك اليوم
          </p>
          <Link 
            to="/register" 
            className="bg-white text-black px-8 py-4 rounded-lg font-semibold hover:bg-gray-200 transition-all duration-300 transform hover:scale-105 shadow-lg inline-flex items-center gap-2"
          >
            <Star size={20} />
            ابدأ الآن
          </Link>
        </div>
      </section>
    </div>
  )
}

export default Home
